package com.example.message_divert

import android.Manifest
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.telephony.SmsManager
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage

class MyFirebaseMessagingService : FirebaseMessagingService() {

    companion object {
        private const val TAG = "FCMService"
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "MyFirebaseMessagingService created")
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)

        Log.d(TAG, "=== FCM MESSAGE RECEIVED ===")
        Log.d(TAG, "Message ID: ${remoteMessage.messageId}")
        Log.d(TAG, "From: ${remoteMessage.from}")
        Log.d(TAG, "Sent time: ${remoteMessage.sentTime}")
        Log.d(TAG, "Data payload: ${remoteMessage.data}")
        Log.d(TAG, "Notification: ${remoteMessage.notification}")

        // Always show a notification for debugging
        val title = remoteMessage.notification?.title ?: "FCM Message Received"
        val body = remoteMessage.notification?.body ?: "Data: ${remoteMessage.data}"
        showNotification(title, body)

        // Handle data payload - this is where SMS sending should be triggered
        if (remoteMessage.data.isNotEmpty()) {
            Log.d(TAG, "Processing FCM data payload: ${remoteMessage.data}")

            // Check if this is a special SMS notification
            if (isSpecialSmsNotification(remoteMessage.data)) {
                Log.d(TAG, "Special SMS notification detected, processing...")
                processSmsRequest(remoteMessage.data)
            } else {
                Log.d(TAG, "Not a special SMS notification, ignoring")
            }
        } else {
            Log.d(TAG, "No data payload in FCM message")
        }

        Log.d(TAG, "=== FCM MESSAGE PROCESSING COMPLETE ===")
    }

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        Log.d(TAG, "New FCM token received: $token")
        // Send token to your server or handle token refresh
        // You might want to store this in SharedPreferences and send to your API
    }

    private fun showNotification(title: String, body: String) {
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        val channelId = "sms_divert_channel"

        // Create notification channel for Android O and above
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                "SMS Divert Notifications",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for SMS Divert app"
                enableVibration(true)
            }
            notificationManager.createNotificationChannel(channel)
        }

        // Create intent for when notification is tapped
        val intent = Intent(this, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK

        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Build notification
        val notification = NotificationCompat.Builder(this, channelId)
            .setContentTitle(title)
            .setContentText(body)
            .setSmallIcon(android.R.drawable.ic_dialog_info) // You can replace with your own icon
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .build()

        notificationManager.notify(System.currentTimeMillis().toInt(), notification)
    }

    private fun isSpecialSmsNotification(data: Map<String, String>): Boolean {
        // Check if the FCM message contains the required fields for SMS sending
        return data.containsKey("phoneNumber") &&
               data.containsKey("message") &&
               data.containsKey("timestamp") &&
               data.containsKey("signature")
    }

    private fun processSmsRequest(data: Map<String, String>) {
        try {
            val phoneNumber = data["phoneNumber"] ?: ""
            val message = data["message"] ?: ""
            val timestamp = data["timestamp"] ?: ""
            val signature = data["signature"] ?: ""

            Log.d(TAG, "Processing SMS request:")
            Log.d(TAG, "Phone: $phoneNumber")
            Log.d(TAG, "Message: $message")
            Log.d(TAG, "Timestamp: $timestamp")
            Log.d(TAG, "Signature: $signature")

            // Validate required fields
            if (phoneNumber.isEmpty() || message.isEmpty() || timestamp.isEmpty() || signature.isEmpty()) {
                Log.e(TAG, "Invalid FCM payload: missing required fields")
                return
            }

            // Send SMS using SmsManager
            sendSmsDirectly(phoneNumber, message)

        } catch (e: Exception) {
            Log.e(TAG, "Error processing SMS request", e)
        }
    }

    private fun sendSmsDirectly(phoneNumber: String, message: String) {
        try {
            Log.d(TAG, "Attempting to send SMS to $phoneNumber")

            // Check SMS permission
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.SEND_SMS) != PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "SMS permission not granted")
                return
            }

            Log.d(TAG, "SMS permission granted, proceeding with send")
            val smsManager = SmsManager.getDefault()

            // Split message if it's too long
            val parts = smsManager.divideMessage(message)
            Log.d(TAG, "Message split into ${parts.size} parts")

            if (parts.size == 1) {
                smsManager.sendTextMessage(phoneNumber, null, message, null, null)
                Log.d(TAG, "Single SMS sent successfully to $phoneNumber")
            } else {
                smsManager.sendMultipartTextMessage(phoneNumber, null, parts, null, null)
                Log.d(TAG, "Multipart SMS sent successfully to $phoneNumber")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to send SMS", e)
        }
    }
}
