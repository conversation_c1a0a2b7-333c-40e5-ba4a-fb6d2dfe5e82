# FCM Service Not Receiving Notifications - Debugging Guide

## Problem
Your Kotlin `MyFirebaseMessagingService` is not receiving FCM notifications at all.

## Step-by-Step Debugging

### Step 1: Test Basic FCM Reception
1. **Run your app** and tap **"Simple FCM Test"**
2. **Copy the FCM token** that appears
3. **Go to Firebase Console** > Cloud Messaging > Send your first message
4. **Paste the token** in "Send to a specific device"
5. **Send a simple notification** (just title and body, no data)
6. **Check if the Flutter app receives it** in the debug log

**Expected Result:** You should see the message in the Flutter debug log
**If this fails:** The issue is with basic FCM setup

### Step 2: Check Android Logs
If you have ADB access, run this command to see if the Kotlin service is being called:
```bash
adb logcat | grep -E "(FCMService|MyFirebaseMessagingService)"
```

**Expected Result:** You should see logs like "FCM MESSAGE RECEIVED" when sending test messages
**If no logs appear:** The Kotlin service is not being triggered

### Step 3: Verify Service Registration
Check if the service is properly registered by looking at the AndroidManifest.xml:

```xml
<service
    android:name=".MyFirebaseMessagingService"
    android:exported="false"
    android:directBootAware="true">
    <intent-filter>
        <action android:name="com.google.firebase.MESSAGING_EVENT" />
    </intent-filter>
</service>
```

### Step 4: Test with Data-Only Messages
Send an FCM message with **only data payload** (no notification):

**Firebase Console:**
1. Go to Cloud Messaging > Send your first message
2. Enter title and body (required by console)
3. Click "Additional options"
4. Add custom data:
   - Key: `phoneNumber`, Value: `09123456789`
   - Key: `message`, Value: `Test SMS`
   - Key: `timestamp`, Value: `1640995200000`
   - Key: `signature`, Value: `test_signature`
5. Send to your FCM token

**Expected Result:** The Kotlin service should receive this and attempt to send SMS

## Common Issues and Fixes

### Issue 1: Service Not Registered Properly
**Symptoms:** No logs from FCMService at all
**Fix:** 
1. Clean and rebuild the project
2. Verify the service class name matches the manifest
3. Make sure the package name is correct

### Issue 2: Firebase Not Initialized in Service
**Symptoms:** Service receives messages but crashes
**Fix:** Already handled in the updated service code

### Issue 3: Permissions Not Granted
**Symptoms:** Service receives FCM but SMS fails
**Fix:** 
1. Check SMS permissions in device settings
2. Grant permissions manually
3. Test SMS sending separately

### Issue 4: Background vs Foreground Handling
**Symptoms:** Works in foreground but not background
**Fix:**
- Use data-only messages (no notification payload)
- The Kotlin service handles background messages
- Flutter handlers only work when app is active

## Testing Commands

### Send FCM via curl (if you have server key):
```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "YOUR_FCM_TOKEN",
    "data": {
      "phoneNumber": "09123456789",
      "message": "Test SMS from curl",
      "timestamp": "'$(date +%s)'000",
      "signature": "test_signature"
    }
  }'
```

### Check if app is receiving FCM at all:
```bash
adb logcat | grep -i firebase
```

### Check SMS permissions:
```bash
adb shell dumpsys package com.example.message_divert | grep -A 1 "android.permission.SEND_SMS"
```

## Debugging Checklist

- [ ] App builds and runs without errors
- [ ] FCM token is generated successfully
- [ ] Simple notification test works (Flutter receives it)
- [ ] Android logs show FCMService activity
- [ ] Data-only FCM messages trigger the Kotlin service
- [ ] SMS permissions are granted
- [ ] SMS sending works independently

## Next Steps Based on Results

**If Step 1 fails (Flutter doesn't receive FCM):**
- Check Firebase configuration files
- Verify internet connectivity
- Check if notifications are enabled for the app

**If Step 1 works but Step 2 fails (no Android logs):**
- The Kotlin service isn't being triggered
- Check service registration in manifest
- Try rebuilding the project

**If Steps 1-2 work but SMS doesn't send:**
- Focus on SMS permissions and implementation
- Test SMS sending separately
- Check phone number format

**If everything works in foreground but not background:**
- Use data-only FCM messages
- Test with app completely closed
- Check background app restrictions

## Important Notes

1. **Use data-only messages** for background SMS sending
2. **The Kotlin service** handles background FCM, not Flutter
3. **Test step by step** - don't skip the basic FCM test
4. **Check Android logs** - they're crucial for debugging the Kotlin service

Start with Step 1 and work your way through. The Simple FCM Test page will help you verify basic FCM functionality before testing the SMS integration.
